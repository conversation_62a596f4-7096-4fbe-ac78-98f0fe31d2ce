import { KycStatusEnum, PlatformType, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildCashbackTransaction,
  buildChargeTransaction,
  buildCreditTicket,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildInvestmentProduct,
  buildKycOperation,
  buildMandate,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSavingsDividend,
  buildSavingsTopup,
  buildSavingsWithdrawal,
  buildSubscription,
  buildTopUpAutomation,
  buildUser,
  buildUserDataRequest,
  buildWealthyhoodDividendTransaction,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import UserService, { TransactionActivityItemType, UpdateUserOptions } from "../userService";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import DateUtil from "../../utils/dateUtil";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import { Reward, RewardDocument } from "../../models/Reward";
import {
  AssetTransactionDocument,
  CashbackTransaction,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DisplayTagEnum,
  DividendTransaction,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  TransactionDocument,
  TransactionInvestmentActivityFilterEnum,
  WealthyhoodDividendTransaction,
  WithdrawalCashTransaction
} from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import { countriesConfig, entitiesConfig, investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { Account } from "../../models/Account";
import logger from "../../external-services/loggerService";
import { BankAccountDocument } from "../../models/BankAccount";
import { MandateDocument } from "../../models/Mandate";
import { AutomationDocument } from "../../models/Automation";
import Decimal from "decimal.js";
import { StripeService } from "../../external-services/stripeService";
import { Subscription } from "../../models/Subscription";
import { OrderDocument } from "../../models/Order";
import { RiskAssessment, RiskAssessmentDocument } from "../../models/RiskAssessment";
import { RedisClientService } from "../../loaders/redis";
import { MixpanelAccountStatusEnum } from "../../external-services/segmentAnalyticsService";
import { SaltedgeService } from "../../external-services/saltedgeService";
import { WealthkernelService } from "../../external-services/wealthkernelService";
import { DepositActionEnum } from "../../configs/depositsConfig";
import { SumsubService } from "../../external-services/sumsubService";
import { buildApplicant } from "../../tests/utils/generateSumsub";
import { FX_TARGET_SPREADS } from "../../configs/fxSpreadsConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("UserService", () => {
  beforeAll(async () => await connectDb("UserService"));
  afterAll(async () => await closeDb());

  describe("updateUser", () => {
    beforeAll(() => {
      jest.spyOn(eventEmitter, "emit");
    });

    describe("when user submits their tax details for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          taxResidency: undefined
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",

            value: "test"
          },
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a taxDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.taxDetailsSubmission.eventId,
          expect.objectContaining({ id: user!.id })
        );
      });
    });

    describe("when user has already submitted their tax details for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",
            value: "test2"
          },
          isUKTaxResident: true
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {
            countryCode: "GB",
            proofType: "NINO",
            value: "test2"
          },
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a taxDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
      });
    });

    describe("when user submits their employment info for the first time", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          employmentInfo: undefined
        });

        const userData: Partial<UserDocument> = {
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          }
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a employmentInfoSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.employmentInfoSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should emit a personalDetailsSubmission event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.personalDetailsSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when user has already submitted their employment info for the first time", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          },
          taxResidencySubmitted: true
        });

        const userData: Partial<UserDocument> = {
          employmentInfo: {
            annualIncome: { amount: 5000, currency: "GBP" },
            incomeRangeId: "1",
            employmentStatus: "Retired",
            sourcesOfWealth: ["Gift"],
            industry: "AgricultureForestryAndFishing"
          }
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a employmentInfoSubmission event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.employmentInfoSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("when user has accepted terms for the first time", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          hasAcceptedTerms: false,
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a termsAccepted event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should store a timestamp in the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBeGreaterThan(
          DateUtil.getDateOfMinutesAgo(1).getTime()
        );
      });

      it("should store a W-8BEN form completion timestamp", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.w8BenForm).toEqual({
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          completedAt: updatedUser.submittedRequiredInfoAt
        });
      });
    });

    describe("when user has already accepted terms for the first time", () => {
      let user: UserDocument;
      const SUBMITTED_TIME = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: SUBMITTED_TIME,
          hasAcceptedTerms: true
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should NOT emit a termsAccepted event", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should not update the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBe(SUBMITTED_TIME.getTime());
      });
    });

    describe("when user has accepted terms for the first time and is KYC failed", () => {
      let user: UserDocument;
      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined,
          hasAcceptedTerms: false,
          kycStatus: KycStatusEnum.FAILED
        });

        const userData: Partial<UserDocument> = {
          hasAcceptedTerms: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("should emit a termsAccepted event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.termsAccepted.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should emit a verification failed event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.verificationFailure.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("should store a timestamp in the submittedRequiredInfoAt field", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(new Date(updatedUser.submittedRequiredInfoAt as Date).getTime()).toBeGreaterThan(
          DateUtil.getDateOfMinutesAgo(1).getTime()
        );
      });
    });

    describe("when user submits only some of the required tax details", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          nationalities: ["GB"],
          dateOfBirth: new Date("1981-02-12"),
          submittedRequiredInfoAt: undefined
        });

        const userData: Partial<UserDocument> = {
          taxResidency: {} as any,
          isUKTaxResident: true
        };

        await UserService.updateUser(user.id, userData);
      });

      it("no taxDetailsSubmission event should be submitted", () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.taxDetailsSubmission.eventId,
          expect.objectContaining({ id: user.id })
        );
      });

      it("the submittedRequiredInfoAt field should be empty", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.submittedRequiredInfoAt).not.toBeDefined();
      });
    });

    describe("when user submits empty string for 'referredByEmail' field to override previous value", () => {
      let user: UserDocument;
      const REFERRER_EMAIL = faker.internet.email().toLowerCase();
      const UPDATED_REFERRER_EMAIL = "";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser({
          referredByEmail: REFERRER_EMAIL
        });

        const userData: Partial<UserDocument> = {
          referredByEmail: UPDATED_REFERRER_EMAIL
        };

        const options: UpdateUserOptions = {
          fieldsToDelete: {
            referredByEmail: true
          }
        };

        await UserService.updateUser(user.id, userData, options);
      });

      it("the 'referredByEmail' field should be undefined", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).not.toBeDefined();
      });
    });

    describe("when user submits empty string for undefined 'referredByEmail' field", () => {
      let user: UserDocument;
      const UPDATED_REFERRER_EMAIL = "";

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();

        const userData: Partial<UserDocument> = {
          referredByEmail: UPDATED_REFERRER_EMAIL
        };

        const options: UpdateUserOptions = {
          fieldsToDelete: {
            referredByEmail: true
          }
        };

        await UserService.updateUser(user.id, userData, options);
      });

      it("the 'referredByEmail' field should be undefined", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.referredByEmail).not.toBeDefined();
      });
    });

    describe("when user submits residency country", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: undefined });
        const account = await buildAccount({
          activeProviders: undefined,
          owner: user._id
        });
        await buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        });

        expect(user.residencyCountry).toBeUndefined();

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should add active providers to the account", async () => {
        const account = await Account.findOne({ owner: user._id });
        expect(account?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });

      it("should add active providers to the real portfolio", async () => {
        const realPortfolio = await Portfolio.findOne({ owner: user._id, mode: PortfolioModeEnum.REAL });
        expect(realPortfolio?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });
    });

    describe("when user submits a different residency country", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: "GR" });
        const account = await buildAccount({
          activeProviders: undefined,
          owner: user._id
        });
        await buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        });

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should add active providers to the account", async () => {
        const account = await Account.findOne({ owner: user._id });
        expect(account?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });

      it("should add active providers to the real portfolio", async () => {
        const realPortfolio = await Portfolio.findOne({ owner: user._id, mode: PortfolioModeEnum.REAL });
        expect(realPortfolio?.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
      });
    });

    describe("when user submits residency country and without any portfolios or account", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ residencyCountry: undefined });

        expect(user.residencyCountry).toBeUndefined();

        await UserService.updateUser(user.id, { residencyCountry: "GB" });
      });
      afterEach(async () => await clearDb());

      it("should update user's residency country", async () => {
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.residencyCountry).toEqual("GB");
      });

      it("should log an error regarding missing account", async () => {
        expect(logger.error).toHaveBeenCalledWith(`Could not add active providers to account for ${user.id}`, {
          module: "UserService",
          method: "updateUser",
          data: {
            userId: user.id
          }
        });
      });

      it("should log an error regarding missing account", async () => {
        expect(logger.error).toHaveBeenCalledWith(
          `Could not add active providers to real portfolio for ${user.id}`,
          {
            module: "UserService",
            method: "updateUser",
            data: {
              userId: user.id
            }
          }
        );
      });
    });
  });

  describe("canReceiveCashback", () => {
    describe("when user has no subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return false", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(false);
      });
    });

    describe("when user has an inactive paid_low subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: false, price: "paid_low_monthly" });
      });
      afterAll(async () => await clearDb());

      it("should return false", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(false);
      });
    });

    describe("when user has an active paid_low subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: true, price: "paid_low_monthly" });
      });
      afterAll(async () => await clearDb());

      it("should return true", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(true);
      });
    });

    describe("when user has an active lifetime subscription", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();

        user = await buildUser();
        await buildSubscription({ owner: user.id, active: true, price: "paid_mid_lifetime_blackfriday_2023" });
      });
      afterAll(async () => await clearDb());

      it("should return true", async () => {
        const canReceiveCashback = await UserService.canReceiveCashback(user);
        expect(canReceiveCashback).toBe(true);
      });
    });
  });

  describe("createAllStripeCustomers", () => {
    describe("when user has not submitted all required info", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer");

        await buildUser({ submittedRequiredInfoAt: undefined });

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and hasn't been created in Stripe", () => {
      let user: UserDocument;

      const stripeId = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer").mockResolvedValue({ id: stripeId });

        user = await buildUser();

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).toHaveBeenCalledTimes(1);
        expect(StripeService.Instance.createCustomer).toHaveBeenCalledWith({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            wealthyhoodId: user.id
          }
        });
      });

      it("should update the user document with the Stripe ID", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.providers?.stripe?.id).toEqual(stripeId);
      });
    });

    describe("when user has already been created in Stripe", () => {
      const stripeId = "CU-123";

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(StripeService.Instance, "createCustomer");

        await buildUser({ providers: { stripe: { id: stripeId } } });

        await UserService.createAllStripeCustomers();
      });
      afterAll(async () => await clearDb());

      it("should not call Stripe", async () => {
        expect(StripeService.Instance.createCustomer).not.toHaveBeenCalled();
      });
    });
  });

  describe("createAllSaltedgeLeads", () => {
    describe("when user has not submitted all required info", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead");

        await buildUser({ submittedRequiredInfoAt: undefined, activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should not call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).not.toHaveBeenCalled();
      });
    });

    describe("when user has already been created in Saltedge", () => {
      const saltedgeId = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead");

        await buildUser({ providers: { saltedge: { id: saltedgeId } }, activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should not call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and hasn't been created in Saltedge", () => {
      let user: UserDocument;

      const saltedgeId = faker.string.uuid();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(SaltedgeService.Instance, "createLead").mockResolvedValue({
          data: {
            customer_id: saltedgeId
          }
        });

        user = await buildUser({ activeProviders: [ProviderEnum.SALTEDGE] });

        await UserService.createAllSaltedgeLeads();
      });
      afterAll(async () => await clearDb());

      it("should call Saltedge", async () => {
        expect(SaltedgeService.Instance.createLead).toHaveBeenCalledTimes(1);
        expect(SaltedgeService.Instance.createLead).toHaveBeenCalledWith({
          fullName: user.fullName,
          email: user.email,
          identifier: user.id
        });
      });

      it("should update the user document with the Saltedge ID", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser.providers?.saltedge?.id).toEqual(saltedgeId);
      });
    });
  });

  describe("createAllWkW8BenForms", () => {
    describe("when user is KYC pending", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({ kycStatus: KycStatusEnum.PENDING });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is KYC failed", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.FAILED,
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is KYC passed but submitted all required info less than 10 minutes ago", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        });

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is deleted", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          email: `deleted_${faker.internet.email()}`,
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          }),
          buildUserDataRequest({
            owner: user.id,
            requestType: "disassociation"
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible but already has a W-8BEN form created", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");

        const user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: new Date(),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Pending"
              }
            }
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and does not have W-8BEN form created", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          kycStatus: KycStatusEnum.PASSED,
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(20),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE
          }
        });

        await Promise.all([
          buildAddress({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid()
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Active"
              }
            }
          })
        ]);

        await UserService.createAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledWith({
          partyId: user.providers.wealthkernel.id
        });
      });

      it("should update the user document with the W8_BEN form", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser).toEqual(
          expect.objectContaining({
            w8BenForm: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              completedAt: new Date(ACCEPTED_TERMS_COMPLETION_DATE),
              providers: expect.objectContaining({
                wealthkernel: {
                  id: W_8BEN_FORM_ID,
                  status: "Pending"
                }
              })
            }
          })
        );
      });
    });
  });

  describe("completeAllWkW8BenForms", () => {
    describe("when user does not have a W-8BEN form created", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5)
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible but already has a W-8BEN form completed", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm");

        await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(5),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: new Date(),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Completed"
              }
            }
          }
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should not call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).not.toHaveBeenCalled();
      });
    });

    describe("when user is eligible and does not have W-8BEN form completed", () => {
      let user: UserDocument;

      const W_8BEN_FORM_ID = faker.string.uuid();
      const ACCEPTED_TERMS_COMPLETION_DATE = new Date();

      beforeAll(async () => {
        jest.resetAllMocks();
        jest.spyOn(WealthkernelService.UKInstance, "completeW8BenForm").mockResolvedValue({
          id: W_8BEN_FORM_ID
        });

        user = await buildUser({
          providers: {
            wealthkernel: {
              id: faker.string.uuid()
            }
          },
          submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(15),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            completedAt: ACCEPTED_TERMS_COMPLETION_DATE,
            providers: {
              wealthkernel: {
                id: W_8BEN_FORM_ID,
                status: "Pending"
              }
            }
          }
        });

        await UserService.completeAllWkW8BenForms();
      });
      afterAll(async () => await clearDb());

      it("should call Wealthkernel", async () => {
        expect(WealthkernelService.UKInstance.completeW8BenForm).toHaveBeenCalledTimes(1);
        expect(WealthkernelService.UKInstance.completeW8BenForm).toHaveBeenCalledWith(W_8BEN_FORM_ID, {
          completedAt: ACCEPTED_TERMS_COMPLETION_DATE.toISOString()
        });
      });

      it("should update the user document with the W8_BEN form", async () => {
        const updatedUser = (await User.findById(user.id)) as UserDocument;
        expect(updatedUser).toEqual(
          expect.objectContaining({
            w8BenForm: {
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              completedAt: ACCEPTED_TERMS_COMPLETION_DATE,
              providers: expect.objectContaining({
                wealthkernel: {
                  id: W_8BEN_FORM_ID,
                  status: "Completed"
                }
              })
            }
          })
        );
      });
    });
  });

  describe("getTransactionActivity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;
    let mandate: MandateDocument;
    let automation: AutomationDocument;

    const TODAY = new Date("2023-10-30T11:00:00Z");

    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();

      Date.now = jest.fn(() => TODAY.valueOf());

      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    describe("when user has a pending Truelayer-backed deposit", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a pending Saltedge-backed deposit", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          status: "Pending",
          providers: {
            saltedge: {
              id: faker.string.uuid(),
              customId: faker.string.uuid(),
              status: "accepted"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a pending deposit linked to a portfolio buy", () => {
      it("should return the pending deposit", async () => {
        const deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "PendingDeposit",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
      });
    });

    describe("when user has a cancelled deposit linked to portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "cancelled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id,
            owner: user.id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a pending portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toEqual([
          expect.objectContaining({
            item: expect.objectContaining({ displayTag: DisplayTagEnum.INSTANT_INVEST })
          })
        ]);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a cancelled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Deposit"
          })
        );
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a settled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });
        deposit = await buildDepositCashTransaction({
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          settledAt: new Date(),
          bankAccount: user.bankAccounts[0].id,
          owner: user.id
        });

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should return the deposit", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.item.id === deposit.id)).toHaveLength(1);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
        ).toHaveLength(1);
        expect(
          receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
        ).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit", () => {
      it("should return the settled deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has a cancelled deposit", () => {
      it("should not return the cancelled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "cancelled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a rejected deposit", () => {
      it("should not return the rejected deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "failed"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a settled wealthyhood dividend", () => {
      it("should return the dividend", async () => {
        const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Bonus",
          item: await WealthyhoodDividendTransaction.findOne({
            _id: wealthyhoodDividend._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has pending wealthyhood dividend", () => {
      it("should not return the dividend", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has rejected wealthyhood dividend", () => {
      it("should not return the dividend", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Rejected"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has NotStarted rebalance", () => {
      it("should return the rebalance transaction and should be cancellable", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "NotStarted"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingBuy rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingSell rebalance", () => {
      describe("and there are no orders", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
            expect.objectContaining({
              id: expectedTransaction.item.id,
              isCancellable: true
            })
          );
        });
      });

      describe("and orders are NOT submitted", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin
            })
          ]);

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };
          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                type: expectedTransaction.type,
                activityFilter: expectedTransaction.activityFilter,
                item: expect.objectContaining({
                  id: expectedTransaction.item?.id,
                  isCancellable: true
                })
              })
            ])
          );
        });
      });

      describe("and orders are submitted", () => {
        it("should return the rebalance transaction and should not be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            })
          ]);

          const expectedTransaction = {
            type: "transaction",
            activityFilter: "Rebalance",
            item: await RebalanceTransaction.findOne({
              _id: rebalance._id
            }).populate("orders")
          };
          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                type: expectedTransaction.type,
                activityFilter: expectedTransaction.activityFilter,
                item: expect.objectContaining({
                  id: expectedTransaction.item?.id,
                  isCancellable: false
                })
              })
            ])
          );
        });
      });
    });

    describe("when user has rejected rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Rejected"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has Cancelled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Cancelled"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has Settled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Settled"
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Rebalance",
          item: await RebalanceTransaction.findOne({
            _id: rebalance._id
          }).populate("orders")
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.item.id
          })
        );
      });
    });

    describe("when user has pending dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {}
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has cancelled dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Cancelled"
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has matched dividend", () => {
      it("should not return the dividend", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched"
            }
          }
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has settled dividend", () => {
      it("should return the dividend", async () => {
        const dividend = await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Dividends",
          item: await DividendTransaction.findOne({
            _id: dividend._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has pending reward", () => {
      it("should not return the reward", async () => {
        await buildReward({
          targetUser: user.id,
          accepted: true,
          status: "Pending"
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has settled reward", () => {
      it("should return the reward", async () => {
        const reward = await buildReward({
          targetUser: user.id,
          accepted: true,
          status: "Settled"
        });

        const expectedReward = {
          type: "reward",
          activityFilter: "Rewards",
          item: await Reward.findOne({
            _id: reward._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedReward)))])
        );
      });
    });

    describe("when user has pending cashback", () => {
      it("should return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });
        const cashback = await buildCashbackTransaction({
          owner: user.id,
          status: "Pending",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Bonus",
          item: await CashbackTransaction.findOne({
            _id: cashback._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has settled cashback", () => {
      it("should return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        const cashback = await buildCashbackTransaction({
          owner: user.id,
          status: "Settled",
          activityFilter: "Bonus",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const expectedTransaction = {
          type: "transaction",
          item: await CashbackTransaction.findOne({
            _id: cashback._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelled cashback", () => {
      it("should not return the cashback", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Cancelled",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has active withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has pending withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelling withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelling" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has withdrawal with no wealthkernel status", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: {}
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has cancelled withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelled" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has rejected withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Rejected" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
        expect(receivedTransactions[0].item.status).toEqual("Pending");
      });
    });

    describe("when user has settled withdrawal", () => {
      it("should return the withdrawal", async () => {
        const withdrawal = await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Withdraw",
          item: await WithdrawalCashTransaction.findOne({
            _id: withdrawal._id
          })
        };

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has any type of charge", () => {
      it("should not return any transaction", async () => {
        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await Promise.all([
          buildChargeTransaction({
            owner: user.id
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "commission"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "custody"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "fx"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "executionSpread"
          }),
          buildChargeTransaction({
            chargeMethod: "direct-debit",
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending",
            consideration: {
              currency: "GBP",
              amount: Decimal.mul(1, 100).toNumber()
            },
            subscription: subscription.id
          })
        ]);

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    describe("when user has a pending sell transaction", () => {
      it("should return the transaction", async () => {
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount:
              (transaction?.orders[0] as OrderDocument)?.quantity *
              investmentProduct.currentTicker.pricePerCurrency["GBP"] *
              100,
            displayQuantity: 1
          })
        );

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a pending sell transaction and one order is matched", () => {
      it("should return the transaction", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          }),
          await buildOrder({
            status: "Matched",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_uk"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            executionProgress: expect.objectContaining({
              label: "Partially executed",
              total: 2,
              matched: 1
            })
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a settled sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item;

        expect(receivedTransaction.id).toEqual(transaction.id);
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            activityFilter: "Sell",
            displayAmount: transaction.consideration?.amount,
            displayQuantity: undefined
          })
        );
      });
    });

    describe("when user has a cancelled sell transaction", () => {
      it("should return transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Cancelled",
          originalInvestmentAmount: 150
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a rejected sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Rejected",
          originalInvestmentAmount: 100
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0].item as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: undefined,
            displayQuantity: transaction.orders[0].quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
      });
    });

    describe("when user has a settled buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          originalInvestmentAmount: 100,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.consideration.amount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0].item as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a pending single asset sell transaction, but the order is matched", () => {
      it("should return the transaction without execution progress", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );
      });
    });

    describe("when user has a settled single asset sell transaction", () => {
      it("should return the transaction with activityFilter Sell", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
        expect(receivedTransactions[0].item.id).toEqual(transaction.id);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a settled single asset buy transaction", () => {
      it("should return the transaction with activityFilter Buy", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a cancelled buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          status: "Cancelled"
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1000,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a rejected buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Rejected",
          originalInvestmentAmount: 110,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);
        const receivedTransaction = receivedTransactions[0].item as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 110,
            displayQuantity: undefined
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending gift buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "PendingGift",
          originalInvestmentAmount: 1250
        });

        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const order = await buildOrder({
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1240,
            originalAmount: 1250,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions[0].item).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1250,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending deposit buy transaction", () => {
      describe("and the deposit has authorized status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorized"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has executed status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has authorization_required status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorization_required"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorizing status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorizing"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has a repeating investment that is pending deposit", () => {
      describe("and the deposit has Pending status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Pending"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel but it is the first for its mandate", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              createdWhilePendingMandate: true,
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collected status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 3),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collected"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy",
              item: expect.objectContaining({
                pendingDeposit: expect.objectContaining({
                  isDirectDebitPaymentCollected: true,
                  directDebitProgressPercentage: 0.75
                })
              })
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toEqual([
            expect.objectContaining({
              item: expect.objectContaining({ displayTag: DisplayTagEnum.AUTOPILOT })
            })
          ]);
        });
      });

      describe("and the deposit has no status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {}
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Completed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)[0]
          ).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(1);
        });
      });

      describe("and the deposit has Cancelled status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Cancelled"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Failed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: TransactionActivityItemType[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Failed"
                  }
                }
              },
              settledAt: new Date(),
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getTransactionActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.item.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.item.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has an asset transaction", () => {
      it("should return status 200 and estimated displayed amount & exchange rate for USD-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getTransactionActivity(user);
        expect((transactionsReceived[0].item as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0].item as AssetTransactionDocument).orders[0];

        // For sell orders with free plan, the spread is FX_TARGET_SPREADS.free and it's added multiplicatively
        // Expected rate = FX_RATE * (1 + FX_TARGET_SPREADS.free) rounded to 3 decimals
        const expectedRate = new Decimal(FX_RATE)
          .mul(1 + FX_TARGET_SPREADS.free)
          .toDecimalPlaces(3)
          .toNumber();

        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: {
              rate: expectedRate,
              currency: "USD"
            }
          })
        );
      });

      it("should return status 200 and estimated displayed amount and NO exchange rate for GBP-traded asset", async () => {
        // Current FX rate GBP → USD is more realistic (~1.27)
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92,
            GBP: Decimal.div(1, FX_RATE).toNumber()
          },
          EUR: {
            EUR: 1,
            GBP: 0.86,
            USD: 1.09
          },
          GBP: {
            GBP: 1,
            EUR: 1.16,
            USD: FX_RATE
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getTransactionActivity(user);
        expect((transactionsReceived[0].item as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0].item as AssetTransactionDocument).orders[0];
        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: undefined
          })
        );
      });
    });

    describe("when user has past orders with active & deprecated ISIN", () => {
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock_deprecated_1";
      let transactionsReceived: TransactionActivityItemType[];

      beforeEach(async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: ACTIVE_ASSET_ID }),
          buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID, listed: false })
        ]);

        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransactionActiveIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionActiveIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[ACTIVE_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionActiveIsin.save();

        const assetTransactionDeprecatedIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionDeprecatedIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionDeprecatedIsin.save();

        transactionsReceived = await UserService.getTransactionActivity(user);
      });

      it("should return the orders for both the active and the deprecated isin", async () => {
        expect(transactionsReceived.length).toBe(2);
        expect(
          transactionsReceived.map((order) => (order.item as AssetTransactionDocument).orders[0].isin)
        ).toEqual(
          expect.arrayContaining([ASSET_CONFIG[ACTIVE_ASSET_ID].isin, ASSET_CONFIG[DEPRECATED_ASSET_ID].isin])
        );
      });
    });

    describe("when user has a savings topup", () => {
      describe("and it has a pending deposit", () => {
        it("should not return the transaction", async () => {
          const [deposit, anotherDeposit] = await Promise.all([
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            }),
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            })
          ]);
          await Promise.all([
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: deposit.id,
              status: "PendingDeposit"
            }),
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: anotherDeposit.id,
              status: "Settled"
            })
          ]);

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it's linked to an incomplete deposit", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                version: "v3",
                status: "authorization_required"
              }
            }
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "PendingDeposit"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it's linked to a savings dividend", () => {
        it("should not return the transaction", async () => {
          const savingsTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled"
          });
          await buildSavingsDividend({
            owner: user.id,
            portfolio: portfolio.id,
            linkedSavingsTopup: savingsTopup.id,
            status: "Settled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithPendingStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithPendingStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithPendingStatus.consideration.amount,
                displayDate: savingsTopupWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithPendingStatus = await buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithPendingStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithPendingStatus.consideration.amount,
                displayDate: savingsTopupWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should return the transaction", async () => {
          const savingsTopupWithSettledStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithSettledStatus.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithSettledStatus.consideration.amount,
                displayDate: savingsTopupWithSettledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsTopupWithRejectedStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithRejectedStatus.id,
                status: "Pending",
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithRejectedStatus.consideration.amount,
                displayDate: savingsTopupWithRejectedStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsTopupWithCancelledStatus = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsTopupWithCancelledStatus.id,
                status: "Pending",
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: savingsTopupWithCancelledStatus.consideration.amount,
                displayDate: savingsTopupWithCancelledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'DepositFailed' status", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "DepositFailed"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings withdrawal", () => {
      describe("and it has 'Pending' status", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithPendingStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithPendingStatus = await buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'PendingTopUp' status", () => {
        it("should return the transaction", async () => {
          const pendignSavingTopup = await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            createdAt: new Date("2024-01-01")
          });
          const savingsWithdrawalWithPendingTopupStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingTopUp",
            createdAt: new Date("2024-01-02")
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithPendingTopupStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithPendingTopupStatus.consideration.amount,
                displayDate: savingsWithdrawalWithPendingTopupStatus.createdAt
              })
            },
            {
              activityFilter: "Withdraw",
              type: "transaction",
              item: expect.objectContaining({
                id: pendignSavingTopup.id,
                displayTitle: "Cash Balance -> Savings GBP",
                displayAmount: pendignSavingTopup.consideration.amount,
                displayDate: pendignSavingTopup.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should return the transaction", async () => {
          const savingsWithdrawalWithSettledStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);

          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithSettledStatus.id,
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithSettledStatus.consideration.amount,
                displayDate: savingsWithdrawalWithSettledStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsWithdrawalWithRejectedStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);
          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithRejectedStatus.id,
                status: "Pending",
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithRejectedStatus.consideration.amount,
                displayDate: savingsWithdrawalWithRejectedStatus.createdAt
              })
            }
          ]);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should return the transaction and ovewrite the status to 'Pending'", async () => {
          const savingsWithdrawalWithCancelledStatus = await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getTransactionActivity(user);
          expect(receivedTransactions).toEqual([
            {
              activityFilter: "Deposit",
              type: "transaction",
              item: expect.objectContaining({
                id: savingsWithdrawalWithCancelledStatus.id,
                status: "Pending",
                displayTitle: "Savings GBP -> Cash Balance",
                displayAmount: savingsWithdrawalWithCancelledStatus.consideration.amount,
                displayDate: savingsWithdrawalWithCancelledStatus.createdAt
              })
            }
          ]);
        });
      });
    });

    describe("when user has a savings dividend", () => {
      it("should not return the transaction", async () => {
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });

        const receivedTransactions = await UserService.getTransactionActivity(user);

        expect(receivedTransactions).toHaveLength(0);
      });
    });
  });

  describe("getInvestmentActivity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;
    let mandate: MandateDocument;
    let automation: AutomationDocument;

    const TODAY = new Date("2023-10-30T11:00:00Z");

    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();

      Date.now = jest.fn(() => TODAY.valueOf());

      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      portfolio = await buildPortfolio({ owner: user.id });
    });

    // buy

    describe("when user has a pending deposit linked to a portfolio buy", () => {
      describe("and the deposit has authorized status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorized"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has executed status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorization_required status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorization_required",
                  version: "v3"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has authorizing status in truelayer", () => {
        let pendingDepositLinkedToAssetTransaction: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });

          pendingDepositLinkedToAssetTransaction = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "authorizing",
                  version: "v3"
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToAssetTransaction,
            status: "PendingDeposit"
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToAssetTransaction.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has a cancelled deposit linked to portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "cancelled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id,
            owner: user.id,
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a pending portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a cancelled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        deposit = await buildDepositCashTransaction(
          {
            depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            settledAt: new Date(),
            bankAccount: user.bankAccounts[0].id
          },
          user
        );

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Cancelled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            investmentActivityFilter: TransactionInvestmentActivityFilterEnum.Buy
          })
        );
      });
    });

    describe("when user has a settled deposit linked to a settled portfolio buy", () => {
      let deposit: DepositCashTransactionDocument;
      let assetTransaction: AssetTransactionDocument;

      beforeEach(async () => {
        await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });
        deposit = await buildDepositCashTransaction({
          depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          settledAt: new Date(),
          bankAccount: user.bankAccounts[0].id,
          owner: user.id
        });

        assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          consideration: { currency: "GBP", amount: 983 },
          pendingDeposit: deposit
        });
      });

      it("should not return the deposit", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === deposit.id)).toHaveLength(0);
      });

      it("should return the asset transaction", async () => {
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)).toHaveLength(
          1
        );
        expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a settled buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          originalInvestmentAmount: 100,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.consideration.amount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0] as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a settled single asset buy transaction", () => {
      it("should return the transaction with activityFilter Buy", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Buy"
        });
      });
    });

    describe("when user has a cancelled buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 1000,
          status: "Cancelled"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1000,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a rejected buy transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "Rejected",
          originalInvestmentAmount: 110,
          consideration: {
            amount: 100, // stored in cents
            currency: "GBP"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 110,
            displayQuantity: undefined
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a pending gift buy transaction", () => {
      it("should return the transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "buy",
          status: "PendingGift",
          originalInvestmentAmount: 1250
        });

        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const order = await buildOrder({
          side: "Buy",
          transaction: transaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          consideration: {
            amount: 1240,
            originalAmount: 1250,
            currency: "GBP"
          }
        });

        transaction.orders = [order];
        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: 1250,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Buy"
          })
        );
      });
    });

    describe("when user has a UK repeating investment that is pending deposit", () => {
      describe("and the deposit has Pending status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Pending"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collecting status in wealthkernel but it is the first for its mandate", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collecting"
                  }
                }
              },
              settledAt: new Date(),
              createdWhilePendingMandate: true,
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Collected status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 5),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 3),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Collected"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              pendingDeposit: expect.objectContaining({
                isDirectDebitPaymentCollected: true,
                directDebitProgressPercentage: 0.75
              })
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has no status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {}
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should NOT return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should NOT return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Completed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              activityFilter: "Buy"
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Cancelled status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Cancelled"
                  }
                }
              },
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has Failed status in wealthkernel", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
              depositMethod: DepositMethodEnum.DIRECT_DEBIT,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              directDebit: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Failed"
                  }
                }
              },
              settledAt: new Date(),
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment,
            status: "DepositFailed",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should not return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(0);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has a EU repeating investment that is pending deposit", () => {
      describe("and the deposit has confirmed status in GoCardless", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
              directDebit: {
                activeProviders: [ProviderEnum.GOCARDLESS],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 1),
                providers: {
                  gocardless: {
                    id: faker.string.uuid(),
                    status: "confirmed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment.id,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              pendingDeposit: expect.objectContaining({
                isDirectDebitPaymentCollected: true,
                directDebitProgressPercentage: 0.5
              })
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has submitted status in GoCardless but the charge date was yesterday", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
              directDebit: {
                activeProviders: [ProviderEnum.GOCARDLESS],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 1),
                providers: {
                  gocardless: {
                    id: faker.string.uuid(),
                    status: "submitted"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment.id,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });

      describe("and the deposit has confirmed status in GoCardless and is in instant money flow", () => {
        let pendingDepositLinkedToScheduledInvestment: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;
        let receivedTransactions: (TransactionDocument | RewardDocument)[];

        beforeAll(async () => {
          const TODAY = new Date("2024-12-18");
          Date.now = jest.fn(() => +TODAY);

          await clearDb();
          user = await buildUser();
          await buildSubscription({ owner: user.id });
          bankAccount = await buildBankAccount({ owner: user.id });
          await user.populate("bankAccounts");
          portfolio = await buildPortfolio({ owner: user.id });
          mandate = await buildMandate({
            owner: user.id,
            bankAccount: user.bankAccounts[0].id,
            providers: {
              gocardless: {
                id: "MAN123",
                status: "active"
              }
            }
          });
          automation = await buildTopUpAutomation({
            owner: user.id,
            mandate: mandate.id,
            portfolio: portfolio.id,
            category: "TopUpAutomation",
            active: true
          });

          const creditTicket = await buildCreditTicket({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Credited"
          });

          pendingDepositLinkedToScheduledInvestment = await buildDepositCashTransaction(
            {
              createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
              directDebit: {
                activeProviders: [ProviderEnum.GOCARDLESS],
                collectionRequestDate: DateUtil.getDateOfDaysAgo(TODAY, 1),
                providers: {
                  gocardless: {
                    id: faker.string.uuid(),
                    status: "confirmed"
                  }
                }
              },
              depositMethod: DepositMethodEnum.DIRECT_DEBIT_AND_BANK_TRANSFER,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST,
              settledAt: new Date(),
              bankAccount: user.bankAccounts[0].id,
              linkedAutomation: automation.id,
              linkedCreditTicket: creditTicket.id
            },
            user
          );
          assetTransaction = await buildAssetTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            portfolioTransactionCategory: "buy",
            pendingDeposit: pendingDepositLinkedToScheduledInvestment.id,
            status: "PendingDeposit",
            linkedAutomation: automation.id
          });
          receivedTransactions = await UserService.getInvestmentActivity(user);
        });

        it("should return the asset transaction", async () => {
          expect(
            receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)
          ).toHaveLength(1);
          expect(receivedTransactions.filter((transaction) => transaction.id === assetTransaction.id)[0]).toEqual(
            expect.objectContaining({
              pendingDeposit: expect.objectContaining({
                isDirectDebitPaymentCollected: true,
                directDebitProgressPercentage: 0.5,
                isMoneyReceived: true
              })
            })
          );
        });

        it("should not return the deposit transaction", async () => {
          expect(
            receivedTransactions.filter(
              (transaction) => transaction.id === pendingDepositLinkedToScheduledInvestment.id
            )
          ).toHaveLength(0);
        });
      });
    });

    describe("when user has an asset transaction and we are considering the exchange rates", () => {
      it("should return status 200 and estimated displayed amount & exchange rate for USD-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_apple", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_apple"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getInvestmentActivity(user);
        expect((transactionsReceived[0] as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0] as AssetTransactionDocument).orders[0];

        // For sell orders with free plan, the spread is FX_TARGET_SPREADS.free and it's added multiplicatively
        // Expected rate = FX_RATE * (1 + FX_TARGET_SPREADS.free) rounded to 3 decimals
        const expectedRate = new Decimal(FX_RATE)
          .mul(1 + FX_TARGET_SPREADS.free)
          .toDecimalPlaces(3)
          .toNumber();

        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: {
              rate: expectedRate,
              currency: "USD"
            }
          })
        );
      });

      it("should return status 200 and estimated displayed amount and NO exchange rate for GBP-traded asset", async () => {
        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92,
            GBP: Decimal.div(1, FX_RATE).toNumber()
          },
          EUR: {
            EUR: 1,
            GBP: 0.86,
            USD: 1.09
          },
          GBP: {
            GBP: 1,
            EUR: 1.16,
            USD: FX_RATE
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const pendingAssetSellTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Pending",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });

        pendingAssetSellTransaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: pendingAssetSellTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: undefined,
            quantity: 1
          })
        ];
        await pendingAssetSellTransaction.save();

        const transactionsReceived = await UserService.getInvestmentActivity(user);
        expect((transactionsReceived[0] as AssetTransactionDocument).displayAmount).toEqual(
          pendingAssetSellTransaction.orders[0].quantity *
            investmentProduct.currentTicker.pricePerCurrency["GBP"] *
            100
        );

        const order = (transactionsReceived[0] as AssetTransactionDocument).orders[0];
        expect(order).toEqual(
          expect.objectContaining({
            displayAmount: order.quantity * investmentProduct.currentTicker.pricePerCurrency["GBP"] * 100,
            displayExchangeRate: undefined
          })
        );
      });
    });

    describe("when user has past orders with active & deprecated ISIN", () => {
      const ACTIVE_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock";
      const DEPRECATED_ASSET_ID: investmentUniverseConfig.AssetType = "equities_blackrock_deprecated_1";
      let transactionsReceived: (TransactionDocument | RewardDocument)[];

      beforeEach(async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: ACTIVE_ASSET_ID }),
          buildInvestmentProduct(false, { assetId: DEPRECATED_ASSET_ID, listed: false })
        ]);

        const FX_RATE = 1.27;
        const FX_RATES = {
          USD: {
            USD: 1,
            EUR: 0.92, // USD → EUR rate
            GBP: Decimal.div(1, FX_RATE).toNumber() // USD → GBP rate (1/1.27 ≈ 0.787)
          },
          EUR: {
            EUR: 1,
            GBP: 0.86, // EUR → GBP rate
            USD: 1.09 // EUR → USD rate
          },
          GBP: {
            GBP: 1,
            EUR: 1.16, // GBP → EUR rate
            USD: FX_RATE // GBP → USD rate
          }
        };
        await RedisClientService.Instance.set("fxRates", FX_RATES);

        const user = await buildUser();

        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransactionActiveIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionActiveIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[ACTIVE_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionActiveIsin.save();

        const assetTransactionDeprecatedIsin = await buildAssetTransaction({
          owner: user.id,
          portfolio,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" }
        });
        assetTransactionDeprecatedIsin.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetTransactionActiveIsin.id,
            isin: investmentUniverseConfig.ASSET_CONFIG[DEPRECATED_ASSET_ID].isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];
        await assetTransactionDeprecatedIsin.save();

        transactionsReceived = await UserService.getInvestmentActivity(user);
      });

      it("should return the orders for both the active and the deprecated isin", async () => {
        expect(transactionsReceived.length).toBe(2);
        expect(transactionsReceived.map((order) => (order as AssetTransactionDocument).orders[0].isin)).toEqual(
          expect.arrayContaining([ASSET_CONFIG[ACTIVE_ASSET_ID].isin, ASSET_CONFIG[DEPRECATED_ASSET_ID].isin])
        );
      });
    });

    // sell
    describe("when user has a pending sell transaction", () => {
      it("should return the transaction", async () => {
        const investmentProduct = await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount:
              (transaction?.orders[0] as OrderDocument)?.quantity *
              investmentProduct.currentTicker.pricePerCurrency["GBP"] *
              100,
            displayQuantity: 1
          })
        );

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );

        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a pending sell transaction and one order is matched", () => {
      it("should return the transaction", async () => {
        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Pending",
          consideration: {
            currency: "USD"
          }
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          }),
          await buildOrder({
            status: "Matched",
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_uk"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();
        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            executionProgress: expect.objectContaining({
              label: "Partially executed",
              total: 2,
              matched: 1
            })
          })
        );

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            investmentActivityFilter: TransactionInvestmentActivityFilterEnum.Sell
          })
        );
      });
    });

    describe("when user has a settled sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0];

        expect(receivedTransaction.id).toEqual(transaction.id);
        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            activityFilter: "Sell",
            displayAmount: transaction.consideration?.amount,
            displayQuantity: undefined
          })
        );
      });
    });

    describe("when user has a cancelled sell transaction", () => {
      it("should return transaction", async () => {
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Cancelled",
          originalInvestmentAmount: 150
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    describe("when user has a rejected sell transaction", () => {
      it("should return the transaction", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us" });

        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "sell",
          status: "Rejected",
          originalInvestmentAmount: 100
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Rejected", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: undefined
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: transaction.originalInvestmentAmount,
            displayQuantity: undefined
          })
        );
        expect((receivedTransactions[0] as AssetTransactionDocument)?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: undefined,
            displayQuantity: transaction.orders[0].quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
      });
    });

    describe("when user has a pending single asset sell transaction, but the order is matched", () => {
      it("should return the transaction without execution progress", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Pending"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).not.toEqual(
          expect.objectContaining({
            executionProgress: expect.anything()
          })
        );
      });
    });

    describe("when user has a settled single asset sell transaction", () => {
      it("should return the transaction with activityFilter Sell", async () => {
        await buildInvestmentProduct(true, { assetId: "equities_us", listed: true });
        const transaction = await buildAssetTransaction({
          owner: user,
          portfolio: portfolio.id,
          portfolioTransactionCategory: "update",
          status: "Settled"
        });

        transaction.orders = [
          await buildOrder({
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Sell",
            transaction: transaction.id,
            isin: investmentUniverseConfig?.ASSET_CONFIG["equities_us"]?.isin,
            quantity: 1,
            consideration: {
              amount: 100, // stored in cents
              currency: "GBP"
            }
          })
        ];

        await transaction.save();

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        const receivedTransaction = receivedTransactions[0] as AssetTransactionDocument;

        expect(receivedTransaction).toEqual(
          expect.objectContaining({
            id: transaction.id,
            displayAmount: receivedTransaction.orders[0]?.displayAmount,
            displayQuantity: receivedTransaction.orders[0]?.displayQuantity
          })
        );
        expect(receivedTransaction?.orders[0]).toEqual(
          expect.objectContaining({
            displayAmount: transaction.orders[0]?.consideration?.amount,
            displayQuantity: transaction.orders[0]?.quantity
          })
        );
        expect.objectContaining({
          activityFilter: "Sell"
        });
        expect(receivedTransactions[0].id).toEqual(transaction.id);
        expect(receivedTransactions[0]).toEqual(
          expect.objectContaining({
            activityFilter: "Sell"
          })
        );
      });
    });

    // rebalance
    describe("when user has NotStarted rebalance", () => {
      it("should return the rebalance transaction and should be cancellable", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "NotStarted"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingBuy rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id,
            isCancellable: true
          })
        );
      });
    });

    describe("when user has PendingSell rebalance", () => {
      describe("and there are no orders", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
            expect.objectContaining({
              id: rebalance.id,
              isCancellable: true
            })
          );
        });
      });

      describe("and orders are NOT submitted", () => {
        it("should return the rebalance transaction and should be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                id: rebalance.id,
                isCancellable: true
              })
            ])
          );
        });
      });

      describe("and orders are submitted", () => {
        it("should return the rebalance transaction and should not be cancellable", async () => {
          await Promise.all([
            buildInvestmentProduct(true, { assetId: "equities_paypal", listed: true }),
            buildInvestmentProduct(true, { assetId: "equities_airbnb", listed: true })
          ]);

          const rebalance = await buildRebalanceTransaction({
            portfolio: portfolio,
            owner: user,
            rebalanceStatus: "PendingSell"
          });

          await Promise.all([
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_paypal"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            }),
            buildOrder({
              side: "Sell",
              transaction: rebalance._id,
              isin: ASSET_CONFIG["equities_airbnb"]?.isin,
              providers: {
                wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() }
              }
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                id: rebalance.id,
                isCancellable: false
              })
            ])
          );
        });
      });
    });

    describe("when user has rejected rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Rejected"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id
          })
        );
      });
    });

    describe("when user has Cancelled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Cancelled"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: rebalance.id
          })
        );
      });
    });

    describe("when user has Settled rebalance", () => {
      it("should return the rebalance transaction", async () => {
        const rebalance = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Settled"
        });

        const expectedTransaction = (await RebalanceTransaction.findOne({
          _id: rebalance._id
        }).populate("orders")) as RebalanceTransactionDocument;

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions[0] as RebalanceTransactionDocument).toEqual(
          expect.objectContaining({
            id: expectedTransaction.id
          })
        );
      });
    });

    // stock dividends

    describe("when the user has stock dividends", () => {
      it("should not return pending dividends", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {}
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return cancelled dividends", async () => {
        await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Cancelled"
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should return settled dividends", async () => {
        const dividend = await buildDividendTransaction({
          owner: user.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(dividend)))])
        );
      });
    });

    // rewards

    describe("when the user has rewards", () => {
      it("should not return pending rewards", async () => {
        await buildReward({
          targetUser: user.id,
          accepted: true,
          status: "Pending"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should return settled rewards", async () => {
        const reward = await buildReward({
          targetUser: user.id,
          accepted: true,
          status: "Settled"
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(reward)))])
        );
      });
    });

    // skipped - cashbacks
    describe("when user has cashback transactions", () => {
      it("should not return pending cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });
        await buildCashbackTransaction({
          owner: user.id,
          status: "Pending",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return settled cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Settled",
          activityFilter: "Bonus",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelled cashbacks", async () => {
        const assetTransactionForCashbacks = await buildAssetTransaction({ portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          status: "Cancelled",
          linkedAssetTransaction: assetTransactionForCashbacks.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });
    });

    // skipped - wealthyhood plan dividend
    describe("when the user has wealthyhood plan dividends", () => {
      it("should not return settled dividends", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return pending dividends", async () => {
        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            }
          }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - deposits
    describe("when user has deposit transactions", () => {
      it("should not return the settled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return the cancelled deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "cancelled"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });

      it("should not return the rejected deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "failed"
            }
          },
          bankAccount: bankAccount.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - withdrawals
    describe("when user has withdrawal transactions", () => {
      it("should not return pending withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelling withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelling" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return cancelled withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Cancelled" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return rejected withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Rejected" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });

      it("should not return settled withdrawals", async () => {
        await buildWithdrawalCashTransaction({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);
        expect(receivedTransactions).toHaveLength(0);
      });
    });

    // skipped transactions - charges
    describe("when user has any type of charge", () => {
      it("should not return any transaction", async () => {
        const subscription = await buildSubscription({
          owner: user.id,
          price: "paid_low_monthly",
          category: "FeeBasedSubscription"
        });

        await Promise.all([
          buildChargeTransaction({
            owner: user.id
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "commission"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "custody"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "fx"
          }),
          buildChargeTransaction({
            owner: user.id,
            chargeType: "executionSpread"
          }),
          buildChargeTransaction({
            chargeMethod: "direct-debit",
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending",
            consideration: {
              currency: "GBP",
              amount: Decimal.mul(1, 100).toNumber()
            },
            subscription: subscription.id
          })
        ]);

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(JSON.parse(JSON.stringify(receivedTransactions))).toHaveLength(0);
      });
    });

    // skipped transactions - savings

    describe("when user has a savings topup", () => {
      describe("and it has a pending deposit", () => {
        it("should not return the transaction", async () => {
          const [deposit, anotherDeposit] = await Promise.all([
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            }),
            buildDepositCashTransaction({
              owner: user.id,
              portfolio: portfolio.id
            })
          ]);
          await Promise.all([
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: deposit.id,
              status: "PendingDeposit"
            }),
            await buildSavingsTopup({
              owner: user.id,
              portfolio: portfolio.id,
              pendingDeposit: anotherDeposit.id,
              status: "Settled"
            })
          ]);

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);

          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'DepositFailed' status", () => {
        it("should not return the transaction", async () => {
          const deposit = await buildDepositCashTransaction({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            pendingDeposit: deposit.id,
            status: "DepositFailed"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings withdrawal", () => {
      describe("and it has 'Pending' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Pending"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Pending' status with no orders", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal(
            {
              owner: user.id,
              portfolio: portfolio.id,
              status: "Pending"
            },
            {},
            false
          );

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'PendingTopUp' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsTopup({
            owner: user.id,
            portfolio: portfolio.id,
            createdAt: new Date("2024-01-01")
          });
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "PendingTopUp",
            createdAt: new Date("2024-01-02")
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Settled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Settled",
            settledAt: new Date()
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Rejected' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Rejected"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });

      describe("and it has 'Cancelled' status", () => {
        it("should not return the transaction", async () => {
          await buildSavingsWithdrawal({
            owner: user.id,
            portfolio: portfolio.id,
            status: "Cancelled"
          });

          const receivedTransactions = await UserService.getInvestmentActivity(user);
          expect(receivedTransactions).toHaveLength(0);
        });
      });
    });

    describe("when user has a savings dividend", () => {
      it("should not return the transaction", async () => {
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id
        });

        const receivedTransactions = await UserService.getInvestmentActivity(user);

        expect(receivedTransactions).toHaveLength(0);
      });
    });
  });

  describe("setPassedKycStatusIfEligible", () => {
    describe("when user has active WK account and passed KYC operation but is flagged for passport", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ isPassportVerified: false });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should leave the status to 'pending'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user has active WK account and passed KYC operation but is potentially duplicate", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({ isPotentiallyDuplicateAccount: true });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should leave the status to 'pending'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user is eligible but passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          isPassportVerified: undefined,
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "PASSPORT", mrzLine1: "MRZMRZMRZ" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user hasn't submitted all required info and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          isPassportVerified: undefined,
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        }); // User missing address details
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(buildApplicant());
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user is Greek and in EU entity and has used old ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD", country: "GRC" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.FAILED);
      });
    });

    describe("when user is Greek and in UK entity and has used old ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD", country: "GRC" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user is Greek and in EU entity and has used new ID card and passport check hasn't run yet", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          isPassportVerified: undefined,
          nationalities: ["GR"],
          providers: {
            sumsub: { id: faker.string.uuid() }
          }
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);

        jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
          buildApplicant({
            info: {
              firstName: user.firstName,
              lastName: user.lastName,
              dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
              nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]],
              idDocs: [{ idDocType: "ID_CARD", mrzLine1: "MRZMRZMRZ" }]
            }
          })
        );
      });
      afterEach(async () => await clearDb());

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has active WK account and passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has active WK account and manually passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "ManuallyPassed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "RED"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should set the status to 'passed'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
      });
    });

    describe("when user has a non active WK account and passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
          }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user has an active WK account and a non-passed KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Failed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "RED"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user does not have a KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser();
        await buildAccount({
          owner: user.id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
      });
      afterEach(async () => await clearDb());

      it("should not set the status to 'passed'", async () => {
        const updateUser = await UserService.setPassedKycStatusIfEligible(user.id);

        expect(updateUser.kycStatus).toEqual(KycStatusEnum.PENDING);
      });
    });

    describe("when user was KYC failed but now his WK account is active and passed a KYC operation", () => {
      let user: UserDocument;

      beforeEach(async () => {
        user = await buildUser({
          kycStatus: KycStatusEnum.FAILED,
          kycFailedAt: new Date()
        });
        await Promise.all([
          buildKycOperation({
            owner: user.id,
            status: "Passed",
            providers: {
              sumsub: {
                id: faker.string.uuid(),
                status: "completed",
                decision: "GREEN"
              }
            }
          }),
          buildAccount({
            owner: user.id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
          }),
          buildSubscription({ owner: user.id, active: false }),
          buildAddress({ owner: user.id })
        ]);
      });
      afterEach(async () => await clearDb());

      it("should make the subscription 'active'", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const subscription = await Subscription.findOne({ owner: user.id });

        expect(subscription?.active).toEqual(true);
      });

      it("should create a risk assessment for that user", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const riskAssessments: RiskAssessmentDocument[] = await RiskAssessment.find();
        expect(riskAssessments.length).toBe(1);
      });

      it("should set the status to 'passed' and remove kycFailedAt", async () => {
        await UserService.setPassedKycStatusIfEligible(user.id);

        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.kycStatus).toEqual(KycStatusEnum.PASSED);
        expect(updatedUser?.kycFailedAt).toBeUndefined();
      });
    });
  });

  describe("updatePassportDetailsOnlyIfMissing", () => {
    describe("when the user has already submitted passport details", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser();

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, {
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
          nationality: "GB"
        });
      });
      afterAll(async () => await clearDb());

      it("should not overwrite the existing passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(user.firstName);
        expect(updatedUser?.lastName).toEqual(user.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(user.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual(user.nationalities);
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });

    describe("when the user hasn't submitted passport details previously", () => {
      let user: UserDocument;

      const APPLICANT_ID = faker.string.uuid();
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" }),
        nationality: "GB" as countriesConfig.CountryCodesType
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, APPLICANT_ID, PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.providers?.sumsub?.id).toEqual(APPLICANT_ID);
        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([PASSPORT_DETAILS.nationality]);
      });

      it("should emit passport details event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.passportDetailsSubmission.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when the user hasn't submitted passport details previously but nationality is missing", () => {
      let user: UserDocument;
      const PASSPORT_DETAILS = {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        dateOfBirth: faker.date.between({ from: "1960-01-01", to: "2000-01-05" })
      };

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({}, false, true);

        await UserService.updateKycProviderDataIfMissing(user.id, faker.string.uuid(), PASSPORT_DETAILS);
      });
      afterAll(async () => await clearDb());

      it("should update user with new passport details", async () => {
        const updatedUser = await User.findById(user._id);

        expect(updatedUser?.firstName).toEqual(PASSPORT_DETAILS.firstName);
        expect(updatedUser?.lastName).toEqual(PASSPORT_DETAILS.lastName);
        expect(updatedUser?.dateOfBirth).toEqual(PASSPORT_DETAILS.dateOfBirth);
        expect(updatedUser?.nationalities).toEqual([]);
      });

      it("should not emit any event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalled();
      });
    });
  });

  describe("setFailedKycStatus", () => {
    describe("when the user has submitted required info and accepted terms", () => {
      let user: UserDocument;

      beforeAll(async () => {
        user = await buildUser({ hasAcceptedTerms: true });
        await buildAddress({ owner: user.id });
        await buildSubscription({ owner: user.id, active: true });
      });
      afterAll(async () => await clearDb());

      it("should set the status to 'failed'", async () => {
        const updatedUser = await UserService.setFailedKycStatus(user);

        expect(updatedUser.kycStatus).toEqual(KycStatusEnum.FAILED);
      });

      it("should deactivate user's subscription", async () => {
        const updatedSubscription = await Subscription.findOne({ owner: user.id });

        expect(updatedSubscription?.active).toEqual(false);
      });

      it("should emit a verification failure event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.verificationFailure.eventId,
          expect.objectContaining({ email: user.email })
        );
      });

      it("should emit a 'whAccountStatusUpdate' event", () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.whAccountStatusUpdate.eventId,
          expect.objectContaining({
            id: user.id
          }),
          expect.objectContaining({
            accountStatus: MixpanelAccountStatusEnum.VerificationFailed
          })
        );
      });
    });
  });

  describe("getAccountStatementActivity", () => {
    describe("when user has no activity", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
      });
      afterAll(async () => await clearDb());

      it("should return empty array", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has an asset transaction with both cancelled / matched orders", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await Promise.all([
          buildInvestmentProduct(true, { assetId: "equities_us", listed: true }),
          buildInvestmentProduct(true, { assetId: "equities_uk", listed: true })
        ]);

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled"
        });

        assetTransaction.orders = await Promise.all([
          buildOrder({
            side: "Buy",
            status: "Matched",
            transaction: assetTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin
          }),
          buildOrder({
            side: "Buy",
            status: "Cancelled",
            transaction: assetTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin
          })
        ]);
        await assetTransaction.save();
      });
      afterAll(async () => await clearDb());

      it("should return only matched order", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            asset: "iShares FTSE 100 UCITS ETF DIST £ (ISF)"
          })
        ]);
      });
    });

    describe("when user has a settled deposit transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the deposit", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "deposit"
          })
        ]);
      });
    });

    describe("when user has a pending deposit transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDepositCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the deposit", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled withdrawal transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction(
          {
            owner: user.id,
            portfolio: portfolio.id,
            settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            consideration: {
              amount: 1000,
              currency: "GBP"
            }
          },
          false
        );
      });
      afterAll(async () => await clearDb());

      it("should return the withdrawal", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "withdrawal"
          })
        ]);
      });
    });

    describe("when user has a pending cashback transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          linkedAssetTransaction: assetTransaction.id
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the cashback", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled cashback transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });
        const assetTransaction = await buildAssetTransaction({ owner: user.id, portfolio: portfolio.id });

        await buildCashbackTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          linkedAssetTransaction: assetTransaction.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return the cashback", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "cashback"
          })
        ]);
      });
    });

    describe("when user has a pending WH dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the WH dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled WH dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWealthyhoodDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the WH dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "bonus"
          })
        ]);
      });
    });

    describe("when user has a pending dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_uk"
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled dividend transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildDividendTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          asset: "equities_uk",
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "dividend"
          })
        ]);
      });
    });

    describe("when user has a pending withdrawal transaction", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the withdrawal", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when the user has a settled reinvested savings dividend", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        const topup = await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
        await buildSavingsDividend({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          consideration: { amount: 100, currency: "GBP" },
          originalDividendAmount: 110,
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          linkedSavingsTopup: topup.id
        });
      });
      afterAll(async () => await clearDb());

      it("should return both savings transactions (top-up and dividend)", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              amount: 1,
              asset: "ICS Sterling Liquidity Fund (Premier)",
              currency: "GBP",
              isin: "IE00B3L10356",
              type: "interest"
            }),
            expect.objectContaining({
              amount: 1,
              asset: "ICS Sterling Liquidity Fund (Premier)",
              currency: "GBP",
              isin: "IE00B3L10356",
              side: "Buy",
              type: "interest reinvestment"
            })
          ])
        );
      });
    });

    describe("when the user has a settled savings topup", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual([
          expect.objectContaining({
            amount: 1,
            asset: "ICS Sterling Liquidity Fund (Premier)",
            currency: "GBP",
            isin: "IE00B3L10356",
            side: "Buy",
            type: "order"
          })
        ]);
      });
    });

    describe("when the user has a settled savings withdrawal", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();
        const portfolio = await buildPortfolio({ owner: user.id });
        await buildSavingsWithdrawal({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          settledAt: DateUtil.getDateOfDaysAgo(TODAY, 1),
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 2),
          consideration: { amount: 100, currency: "GBP" }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the dividend", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);

        expect(activity).toEqual([
          expect.objectContaining({
            amount: 1,
            asset: "ICS Sterling Liquidity Fund (Premier)",
            currency: "GBP",
            isin: "IE00B3L10356",
            side: "Sell",
            type: "order"
          })
        ]);
      });
    });

    describe("when user has a pending reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          updatedAt: new Date(),
          status: "Pending",
          consideration: {
            currency: "GBP",
            amount: 1000,
            bonusAmount: 1000,
            orderAmount: 1000
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the reward", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a settled reward", () => {
      let user: UserDocument;

      beforeAll(async () => {
        const TODAY = new Date("2024-01-10");
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        await buildReward({
          targetUser: user.id,
          asset: "equities_uk",
          updatedAt: new Date(),
          status: "Settled",
          consideration: {
            currency: "GBP",
            amount: 1000,
            bonusAmount: 1000,
            orderAmount: 1000
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          }
        });
      });
      afterAll(async () => await clearDb());

      it("should return the reward", async () => {
        const activity = await UserService.getAccountStatementActivity(user.id);
        expect(activity).toEqual([
          expect.objectContaining({
            amount: 10,
            currency: "GBP",
            type: "reward"
          })
        ]);
      });
    });

    describe("when user has a transaction that would be included but was created BEFORE the requested time period", () => {
      const TODAY = new Date("2024-01-10");

      let user: UserDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 30)
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the transaction", async () => {
        const activity = await UserService.getAccountStatementActivity(
          user.id,
          DateUtil.getDateOfDaysAgo(TODAY, 10)
        );
        expect(activity).toEqual([]);
      });
    });

    describe("when user has a transaction that would be included but was created AFTER the requested time period", () => {
      const TODAY = new Date("2024-01-10");

      let user: UserDocument;

      beforeAll(async () => {
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser();

        const portfolio = await buildPortfolio({ owner: user.id });

        await buildWithdrawalCashTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          consideration: {
            amount: 1000,
            currency: "GBP"
          },
          createdAt: DateUtil.getDateOfDaysAgo(TODAY, 10)
        });
      });
      afterAll(async () => await clearDb());

      it("should NOT return the transaction", async () => {
        const activity = await UserService.getAccountStatementActivity(
          user.id,
          DateUtil.getDateOfDaysAgo(TODAY, 30),
          DateUtil.getDateOfDaysAgo(TODAY, 20)
        );
        expect(activity).toEqual([]);
      });
    });
  });

  describe("updateDeviceToken", () => {
    it("should throw an error for an invalid platform", async () => {
      const platform = "invalidPlatform";
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await expect(UserService.updateDeviceToken(user.id, platform as any, token)).rejects.toThrow(
        "Platform must be one of: android, ios"
      );
    });

    it("should update the device token for a valid platform and token", async () => {
      const platform = "ios";
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await UserService.updateDeviceToken(user.id, platform, token);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser.deviceTokens[platform]).toBe(token);
    });
  });

  describe("removeDeviceToken", () => {
    it("should throw an error for an invalid platform", async () => {
      const platform = "invalidPlatform";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      await expect(UserService.removeDeviceToken(user.id, platform as any)).rejects.toThrow(
        "Platform must be one of: android, ios"
      );
    });

    it("should remove the device token for a valid platform", async () => {
      const platform = "ios" as PlatformType;
      const token = "validToken";
      const user = await buildUser({ deviceTokens: { ios: "oldToken" } });

      // First, update the device token
      await UserService.updateDeviceToken(user.id, platform, token);

      // Then, remove the device token
      await UserService.removeDeviceToken(user.id, platform);

      const updatedUser = await User.findById(user.id);
      expect(updatedUser.deviceTokens[platform]).toBeUndefined();
    });
  });
});
